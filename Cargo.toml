[package]
name = "waybar-virtual-desktops-cffi"
version = "1.0.0"
edition = "2021"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
description = "CFFI module for Waybar to display Hyprland virtual desktops"
license = "MIT"

[lib]
crate-type = ["cdylib"]

[dependencies]
# Waybar CFFI interface
waybar-cffi = "0.1"

# JSON handling for configuration
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Async runtime for IPC handling
tokio = { version = "1.0", features = ["net", "rt", "rt-multi-thread", "macros", "sync", "time", "io-util"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Random number generation for jitter
fastrand = "2.0"

# Logging
log = "0.4"
env_logger = "0.10"

# String handling
once_cell = "1.19"

# Regular expressions for security validation
regex = "1.10"

# Concurrent data structures for metrics
dashmap = "5.5"

[dev-dependencies]
# For integration tests
futures = "0.3"

[build-dependencies]
# For generating bindings if needed
bindgen = "0.69"
