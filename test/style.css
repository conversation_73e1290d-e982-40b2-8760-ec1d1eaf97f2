/* Virtual Desktop CFFI Test Styling - Material You Design */

/* Material You Color Scheme:
 * background: #141318
 * on_surface: #e6e1e9
 * primary: #cdbdff
 * on_primary: #36343a
 * surface_container: #201f24
 * surface_container_high: #2b292f
 * surface_container_highest: #36343a
 * surface_container_low: #1c1b20
 * surface_container_lowest: #0f0d13
 * surface_dim: #141318
 * surface_tint: #cdbdff
 * surface_variant: #48454e
 */

/* Global Styling */
* {
    font-size: 15px;
    font-family: "CodeNewRoman Nerd Font Propo", "JetBrainsMono Nerd Font", monospace;
}

/* Waybar Window */
window#waybar {
    all: unset;
}

/* Center modules container - matching your original approach */
.modules-center {
    padding: 7px;
    margin: 10px 0 5px 0;
    border-radius: 10px;
    background: rgba(20, 19, 24, 0.6); /* alpha(background, 0.6) */
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.6);
}

/* Virtual Desktop CFFI Module - Material You Styling */
#cffi-virtual_desktops {
    padding: 0px 5px;
}

/* Individual Virtual Desktop Labels - matching your original button approach */
#cffi-virtual_desktops label {
    all: unset;
    padding: 0px 8px;
    margin: 0 4px; /* Increased spacing from 2px to 4px */
    transition: all 0.2s ease; /* Matching your original 0.2s timing */
    border-radius: 4px;
    color: rgba(205, 189, 255, 0.4); /* alpha(primary, 0.4) - default state */
}

/* Focused virtual desktop - matching your active workspaces */
#cffi-virtual_desktops label.vdesk-focused {
    color: #cdbdff; /* primary */
    border: none;
    text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.5);
    transition: all 0.2s ease;
}

/* Unfocused virtual desktop - matching your empty workspaces */
#cffi-virtual_desktops label.vdesk-unfocused {
    color: rgba(0, 0, 0, 0); /* Transparent text */
    border: none;
    text-shadow: 0px 0px 1.5px rgba(0, 0, 0, 0.2); /* Subtle shadow outline */
    transition: all 0.2s ease;
}

/* General hover effects - for any label */
#cffi-virtual_desktops label:hover {
    color: rgba(0, 0, 0, 0);
    border: none;
    text-shadow: 0px 0px 1.5px rgba(0, 0, 0, 0.5); /* Enhanced shadow on hover */
    transition: all 1s ease; /* Slow transition like your original */
}

/* Focused hover - should maintain primary color */
#cffi-virtual_desktops label.vdesk-focused:hover {
    color: #cdbdff; /* Keep primary color */
    text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.7); /* Slightly enhanced shadow */
    transition: all 1s ease;
}

/* Hidden virtual desktops (smooth disappearance) */
#cffi-virtual_desktops label.hidden {
    padding: 0;
    margin: 0;
    opacity: 0;
    transition: all 0.15s ease;
}

/* Tooltip styling - matching your original */
tooltip {
    background: #141318; /* background */
    color: #e6e1e9; /* on_surface */
}