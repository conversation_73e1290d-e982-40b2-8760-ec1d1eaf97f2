#!/usr/bin/env rust-script

//! Performance metrics demonstration for the O(n²) widget reordering optimization
//! 
//! This script demonstrates the metrics collection in action by simulating
//! various widget reordering scenarios and showing the optimization effectiveness.

use std::sync::Arc;
use std::collections::HashMap;

// Simulate the metrics system
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::Instant;

/// Simple performance metrics focused on the O(n²) optimization
#[derive(Debug)]
pub struct PerformanceMetrics {
    widget_reorder_count: AtomicU64,
    widget_reorder_optimized_count: AtomicU64,
    widget_update_duration_micros: AtomicU64,
    ipc_error_count: AtomicU64,
    uptime_start: Instant,
}

impl PerformanceMetrics {
    pub fn new() -> Self {
        Self {
            widget_reorder_count: AtomicU64::new(0),
            widget_reorder_optimized_count: AtomicU64::new(0),
            widget_update_duration_micros: AtomicU64::new(0),
            ipc_error_count: AtomicU64::new(0),
            uptime_start: Instant::now(),
        }
    }
    
    pub fn record_widget_reorder(&self, was_optimized: bool) {
        self.widget_reorder_count.fetch_add(1, Ordering::Relaxed);
        if was_optimized {
            self.widget_reorder_optimized_count.fetch_add(1, Ordering::Relaxed);
        }
    }
    
    pub fn log_summary(&self) {
        let reorders_total = self.widget_reorder_count.load(Ordering::Relaxed);
        let reorders_optimized = self.widget_reorder_optimized_count.load(Ordering::Relaxed);
        let optimization_rate = if reorders_total > 0 { 
            reorders_optimized as f64 / reorders_total as f64 
        } else { 0.0 };
        
        println!("Widget Reordering Performance:");
        println!("  Uptime: {}s", self.uptime_start.elapsed().as_secs());
        println!("  Reorders: {} total, {} optimized ({:.1}% optimization rate)", 
                reorders_total, reorders_optimized, optimization_rate * 100.0);
    }
}

/// Simulate the optimized reordering algorithm
fn simulate_reordering(current_order: Vec<u32>, new_order: Vec<u32>, metrics: &PerformanceMetrics) -> usize {
    if new_order == current_order {
        // Record that reordering was optimized (no moves needed)
        metrics.record_widget_reorder(true);
        return 0;
    }

    // Build position maps for efficient lookup
    let current_positions: HashMap<u32, usize> = current_order
        .iter()
        .enumerate()
        .map(|(pos, &id)| (id, pos))
        .collect();

    let target_positions: HashMap<u32, usize> = new_order
        .iter()
        .enumerate()
        .map(|(pos, &id)| (id, pos))
        .collect();

    // Collect widgets that need to be moved
    let mut moves_needed = Vec::new();
    for (widget_id, &target_pos) in &target_positions {
        if let Some(&current_pos) = current_positions.get(widget_id) {
            if current_pos != target_pos {
                moves_needed.push((*widget_id, target_pos));
            }
        }
    }

    // Only perform operations if moves are actually needed
    let was_optimized = moves_needed.len() < new_order.len();
    let moves_count = moves_needed.len();
    
    if !moves_needed.is_empty() {
        println!("  Optimized reordering: moving {} out of {} widgets", moves_count, new_order.len());
    } else {
        println!("  Optimized reordering: no moves needed");
    }

    // Record reordering metrics
    metrics.record_widget_reorder(was_optimized);
    
    moves_count
}

fn main() {
    println!("🚀 Widget Reordering Performance Test");
    println!("=====================================\n");
    
    let metrics = PerformanceMetrics::new();
    
    // Test Case 1: No changes needed (best case optimization)
    println!("Test 1: No changes needed");
    let order = vec![1, 2, 3, 4, 5];
    let moves = simulate_reordering(order.clone(), order.clone(), &metrics);
    println!("  Result: {} moves needed\n", moves);
    
    // Test Case 2: Single element move
    println!("Test 2: Single element move");
    let current = vec![1, 2, 3, 4, 5];
    let new = vec![2, 1, 3, 4, 5]; // Swap first two
    let moves = simulate_reordering(current, new, &metrics);
    println!("  Result: {} moves needed\n", moves);
    
    // Test Case 3: Partial reordering
    println!("Test 3: Partial reordering (reverse first 3)");
    let current = vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    let mut new = current.clone();
    new[0..3].reverse(); // [3, 2, 1, 4, 5, 6, 7, 8, 9, 10]
    let moves = simulate_reordering(current, new, &metrics);
    println!("  Result: {} moves needed\n", moves);
    
    // Test Case 4: Complete reversal (worst case)
    println!("Test 4: Complete reversal");
    let current = vec![1, 2, 3, 4, 5];
    let mut new = current.clone();
    new.reverse(); // [5, 4, 3, 2, 1]
    let moves = simulate_reordering(current, new, &metrics);
    println!("  Result: {} moves needed\n", moves);
    
    // Test Case 5: Large widget set with minimal changes
    println!("Test 5: Large widget set (100 widgets) with single swap");
    let large_current: Vec<u32> = (1..=100).collect();
    let mut large_new = large_current.clone();
    large_new.swap(0, 99); // Swap first and last
    let moves = simulate_reordering(large_current, large_new, &metrics);
    println!("  Result: {} moves needed (out of 100 widgets)\n", moves);
    
    // Show final metrics
    println!("=== FINAL PERFORMANCE METRICS ===");
    metrics.log_summary();
    
    println!("\n✅ Performance test completed!");
    println!("The O(n²) → O(k) optimization successfully reduces GTK operations");
    println!("by only moving widgets that actually changed position.");
}
