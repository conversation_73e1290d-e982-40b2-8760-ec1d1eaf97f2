//! Performance metrics and observability framework for the virtual desktops module

use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use std::collections::HashMap;

use dashmap::DashMap;
use serde::Serialize;

use crate::errors::ErrorSeverity;

/// Performance metrics collector for monitoring module health and performance
#[derive(Debug)]
pub struct PerformanceMetrics {
    // IPC performance metrics
    ipc_latency_histogram: LatencyHistogram,
    ipc_request_count: AtomicU64,
    ipc_error_count: AtomicU64,
    
    // Widget update performance
    widget_update_duration: LatencyHistogram,
    widget_reorder_count: AtomicU64,
    widget_reorder_optimized_count: AtomicU64,
    
    // Error tracking by severity
    error_rate_by_severity: DashMap<ErrorSeverity, AtomicU64>,
    
    // Event monitoring metrics
    event_processing_duration: LatencyHistogram,
    events_processed: AtomicU64,
    events_dropped: AtomicU64,
    
    // Memory and resource usage
    active_widgets: AtomicU64,
    memory_allocations: AtomicU64,
    
    // Module lifecycle metrics
    startup_time: AtomicU64,
    uptime_start: Instant,
}

/// Histogram for tracking latency distributions
#[derive(Debug)]
pub struct LatencyHistogram {
    // Buckets for different latency ranges (in microseconds)
    bucket_0_1ms: AtomicU64,      // 0-1ms
    bucket_1_5ms: AtomicU64,      // 1-5ms
    bucket_5_10ms: AtomicU64,     // 5-10ms
    bucket_10_50ms: AtomicU64,    // 10-50ms
    bucket_50_100ms: AtomicU64,   // 50-100ms
    bucket_100ms_plus: AtomicU64, // 100ms+
    
    // Summary statistics
    total_count: AtomicU64,
    total_duration_micros: AtomicU64,
    min_duration_micros: AtomicU64,
    max_duration_micros: AtomicU64,
}

/// Timer for measuring operation duration
pub struct MetricsTimer {
    start: Instant,
    metrics: Arc<PerformanceMetrics>,
    operation: TimerOperation,
}

/// Types of operations that can be timed
#[derive(Debug, Clone)]
pub enum TimerOperation {
    IpcRequest,
    WidgetUpdate,
    EventProcessing,
}

/// Snapshot of current metrics for reporting
#[derive(Debug, Serialize)]
pub struct MetricsSnapshot {
    pub timestamp: u64,
    pub uptime_seconds: u64,
    
    // IPC metrics
    pub ipc_requests_total: u64,
    pub ipc_errors_total: u64,
    pub ipc_latency: LatencySnapshot,
    
    // Widget metrics
    pub widget_updates_total: u64,
    pub widget_reorders_total: u64,
    pub widget_reorders_optimized: u64,
    pub widget_update_latency: LatencySnapshot,
    pub active_widgets: u64,
    
    // Event metrics
    pub events_processed_total: u64,
    pub events_dropped_total: u64,
    pub event_processing_latency: LatencySnapshot,
    
    // Error metrics
    pub errors_by_severity: HashMap<String, u64>,
    
    // Performance ratios
    pub ipc_error_rate: f64,
    pub event_drop_rate: f64,
    pub reorder_optimization_rate: f64,
}

#[derive(Debug, Serialize)]
pub struct LatencySnapshot {
    pub count: u64,
    pub avg_micros: f64,
    pub min_micros: u64,
    pub max_micros: u64,
    pub p50_bucket: String,
    pub p95_bucket: String,
    pub p99_bucket: String,
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self::new()
    }
}

impl PerformanceMetrics {
    /// Create a new metrics collector
    pub fn new() -> Self {
        Self {
            ipc_latency_histogram: LatencyHistogram::new(),
            ipc_request_count: AtomicU64::new(0),
            ipc_error_count: AtomicU64::new(0),
            
            widget_update_duration: LatencyHistogram::new(),
            widget_reorder_count: AtomicU64::new(0),
            widget_reorder_optimized_count: AtomicU64::new(0),
            
            error_rate_by_severity: DashMap::new(),
            
            event_processing_duration: LatencyHistogram::new(),
            events_processed: AtomicU64::new(0),
            events_dropped: AtomicU64::new(0),
            
            active_widgets: AtomicU64::new(0),
            memory_allocations: AtomicU64::new(0),
            
            startup_time: AtomicU64::new(0),
            uptime_start: Instant::now(),
        }
    }
    
    /// Start timing an operation
    pub fn start_timer(&self, operation: TimerOperation, metrics: Arc<PerformanceMetrics>) -> MetricsTimer {
        MetricsTimer {
            start: Instant::now(),
            metrics,
            operation,
        }
    }
    
    /// Record an IPC request
    pub fn record_ipc_request(&self) {
        self.ipc_request_count.fetch_add(1, Ordering::Relaxed);
    }
    
    /// Record an IPC error
    pub fn record_ipc_error(&self) {
        self.ipc_error_count.fetch_add(1, Ordering::Relaxed);
    }
    
    /// Record a widget reorder operation
    pub fn record_widget_reorder(&self, was_optimized: bool) {
        self.widget_reorder_count.fetch_add(1, Ordering::Relaxed);
        if was_optimized {
            self.widget_reorder_optimized_count.fetch_add(1, Ordering::Relaxed);
        }
    }
    
    /// Record an error by severity
    pub fn record_error(&self, severity: ErrorSeverity) {
        self.error_rate_by_severity
            .entry(severity)
            .or_insert_with(|| AtomicU64::new(0))
            .fetch_add(1, Ordering::Relaxed);
    }
    
    /// Record event processing
    pub fn record_event_processed(&self) {
        self.events_processed.fetch_add(1, Ordering::Relaxed);
    }
    
    /// Record dropped event
    pub fn record_event_dropped(&self) {
        self.events_dropped.fetch_add(1, Ordering::Relaxed);
    }
    
    /// Update active widget count
    pub fn set_active_widgets(&self, count: u64) {
        self.active_widgets.store(count, Ordering::Relaxed);
    }
    
    /// Record startup completion
    pub fn record_startup_complete(&self, duration: Duration) {
        self.startup_time.store(duration.as_micros() as u64, Ordering::Relaxed);
    }
    
    /// Get current metrics snapshot
    pub fn snapshot(&self) -> MetricsSnapshot {
        let uptime = self.uptime_start.elapsed();
        let events_total = self.events_processed.load(Ordering::Relaxed);
        let events_dropped = self.events_dropped.load(Ordering::Relaxed);
        let ipc_total = self.ipc_request_count.load(Ordering::Relaxed);
        let ipc_errors = self.ipc_error_count.load(Ordering::Relaxed);
        let reorders_total = self.widget_reorder_count.load(Ordering::Relaxed);
        let reorders_optimized = self.widget_reorder_optimized_count.load(Ordering::Relaxed);
        
        let errors_by_severity: HashMap<String, u64> = self.error_rate_by_severity
            .iter()
            .map(|entry| (format!("{:?}", entry.key()), entry.value().load(Ordering::Relaxed)))
            .collect();
        
        MetricsSnapshot {
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            uptime_seconds: uptime.as_secs(),
            
            ipc_requests_total: ipc_total,
            ipc_errors_total: ipc_errors,
            ipc_latency: self.ipc_latency_histogram.snapshot(),
            
            widget_updates_total: self.widget_update_duration.total_count(),
            widget_reorders_total: reorders_total,
            widget_reorders_optimized: reorders_optimized,
            widget_update_latency: self.widget_update_duration.snapshot(),
            active_widgets: self.active_widgets.load(Ordering::Relaxed),
            
            events_processed_total: events_total,
            events_dropped_total: events_dropped,
            event_processing_latency: self.event_processing_duration.snapshot(),
            
            errors_by_severity,
            
            ipc_error_rate: if ipc_total > 0 { ipc_errors as f64 / ipc_total as f64 } else { 0.0 },
            event_drop_rate: if events_total + events_dropped > 0 { 
                events_dropped as f64 / (events_total + events_dropped) as f64 
            } else { 0.0 },
            reorder_optimization_rate: if reorders_total > 0 { 
                reorders_optimized as f64 / reorders_total as f64 
            } else { 0.0 },
        }
    }
    
    /// Log metrics summary
    pub fn log_summary(&self) {
        let snapshot = self.snapshot();
        log::info!("Performance Metrics Summary:");
        log::info!("  Uptime: {}s", snapshot.uptime_seconds);
        log::info!("  IPC: {} requests, {} errors ({:.2}% error rate)", 
                  snapshot.ipc_requests_total, snapshot.ipc_errors_total, snapshot.ipc_error_rate * 100.0);
        log::info!("  Widgets: {} active, {} updates, avg {:.2}ms update time", 
                  snapshot.active_widgets, snapshot.widget_updates_total, snapshot.widget_update_latency.avg_micros / 1000.0);
        log::info!("  Events: {} processed, {} dropped ({:.2}% drop rate)", 
                  snapshot.events_processed_total, snapshot.events_dropped_total, snapshot.event_drop_rate * 100.0);
        log::info!("  Reorders: {} total, {} optimized ({:.2}% optimization rate)",
                  snapshot.widget_reorders_total, snapshot.widget_reorders_optimized, snapshot.reorder_optimization_rate * 100.0);
    }
}

impl LatencyHistogram {
    /// Create a new latency histogram
    pub fn new() -> Self {
        Self {
            bucket_0_1ms: AtomicU64::new(0),
            bucket_1_5ms: AtomicU64::new(0),
            bucket_5_10ms: AtomicU64::new(0),
            bucket_10_50ms: AtomicU64::new(0),
            bucket_50_100ms: AtomicU64::new(0),
            bucket_100ms_plus: AtomicU64::new(0),
            total_count: AtomicU64::new(0),
            total_duration_micros: AtomicU64::new(0),
            min_duration_micros: AtomicU64::new(u64::MAX),
            max_duration_micros: AtomicU64::new(0),
        }
    }

    /// Record a duration measurement
    pub fn record(&self, duration: Duration) {
        let micros = duration.as_micros() as u64;

        // Update buckets
        match micros {
            0..=1000 => self.bucket_0_1ms.fetch_add(1, Ordering::Relaxed),
            1001..=5000 => self.bucket_1_5ms.fetch_add(1, Ordering::Relaxed),
            5001..=10000 => self.bucket_5_10ms.fetch_add(1, Ordering::Relaxed),
            10001..=50000 => self.bucket_10_50ms.fetch_add(1, Ordering::Relaxed),
            50001..=100000 => self.bucket_50_100ms.fetch_add(1, Ordering::Relaxed),
            _ => self.bucket_100ms_plus.fetch_add(1, Ordering::Relaxed),
        };

        // Update summary statistics
        self.total_count.fetch_add(1, Ordering::Relaxed);
        self.total_duration_micros.fetch_add(micros, Ordering::Relaxed);

        // Update min (using compare_exchange loop for atomic min)
        let mut current_min = self.min_duration_micros.load(Ordering::Relaxed);
        while micros < current_min {
            match self.min_duration_micros.compare_exchange_weak(
                current_min, micros, Ordering::Relaxed, Ordering::Relaxed
            ) {
                Ok(_) => break,
                Err(actual) => current_min = actual,
            }
        }

        // Update max (using compare_exchange loop for atomic max)
        let mut current_max = self.max_duration_micros.load(Ordering::Relaxed);
        while micros > current_max {
            match self.max_duration_micros.compare_exchange_weak(
                current_max, micros, Ordering::Relaxed, Ordering::Relaxed
            ) {
                Ok(_) => break,
                Err(actual) => current_max = actual,
            }
        }
    }

    /// Get total count of measurements
    pub fn total_count(&self) -> u64 {
        self.total_count.load(Ordering::Relaxed)
    }

    /// Create a snapshot of current histogram state
    pub fn snapshot(&self) -> LatencySnapshot {
        let count = self.total_count.load(Ordering::Relaxed);
        let total_micros = self.total_duration_micros.load(Ordering::Relaxed);
        let min_micros = self.min_duration_micros.load(Ordering::Relaxed);
        let max_micros = self.max_duration_micros.load(Ordering::Relaxed);

        let avg_micros = if count > 0 { total_micros as f64 / count as f64 } else { 0.0 };

        // Calculate percentile buckets (simplified approximation)
        let bucket_counts = [
            self.bucket_0_1ms.load(Ordering::Relaxed),
            self.bucket_1_5ms.load(Ordering::Relaxed),
            self.bucket_5_10ms.load(Ordering::Relaxed),
            self.bucket_10_50ms.load(Ordering::Relaxed),
            self.bucket_50_100ms.load(Ordering::Relaxed),
            self.bucket_100ms_plus.load(Ordering::Relaxed),
        ];

        let bucket_names = ["0-1ms", "1-5ms", "5-10ms", "10-50ms", "50-100ms", "100ms+"];

        let (p50_bucket, p95_bucket, p99_bucket) = if count > 0 {
            let p50_threshold = count / 2;
            let p95_threshold = (count * 95) / 100;
            let p99_threshold = (count * 99) / 100;

            let mut cumulative = 0;
            let mut p50 = "0-1ms";
            let mut p95 = "0-1ms";
            let mut p99 = "0-1ms";

            for (i, &bucket_count) in bucket_counts.iter().enumerate() {
                cumulative += bucket_count;
                if cumulative >= p50_threshold && p50 == "0-1ms" {
                    p50 = bucket_names[i];
                }
                if cumulative >= p95_threshold && p95 == "0-1ms" {
                    p95 = bucket_names[i];
                }
                if cumulative >= p99_threshold && p99 == "0-1ms" {
                    p99 = bucket_names[i];
                }
            }

            (p50.to_string(), p95.to_string(), p99.to_string())
        } else {
            ("N/A".to_string(), "N/A".to_string(), "N/A".to_string())
        };

        LatencySnapshot {
            count,
            avg_micros,
            min_micros: if min_micros == u64::MAX { 0 } else { min_micros },
            max_micros,
            p50_bucket,
            p95_bucket,
            p99_bucket,
        }
    }
}

impl MetricsTimer {
    /// Complete the timer and record the measurement
    pub fn finish(self) {
        let duration = self.start.elapsed();

        match self.operation {
            TimerOperation::IpcRequest => {
                self.metrics.ipc_latency_histogram.record(duration);
            }
            TimerOperation::WidgetUpdate => {
                self.metrics.widget_update_duration.record(duration);
            }
            TimerOperation::EventProcessing => {
                self.metrics.event_processing_duration.record(duration);
            }
        }
    }
}

impl Drop for MetricsTimer {
    fn drop(&mut self) {
        // Auto-record if timer is dropped without explicit finish()
        let duration = self.start.elapsed();

        match self.operation {
            TimerOperation::IpcRequest => {
                self.metrics.ipc_latency_histogram.record(duration);
            }
            TimerOperation::WidgetUpdate => {
                self.metrics.widget_update_duration.record(duration);
            }
            TimerOperation::EventProcessing => {
                self.metrics.event_processing_duration.record(duration);
            }
        }
    }
}
